# 🚀 SIMDOC DEPLOYMENT - QUICK START

## 📋 Ringkasan Cepat

<PERSON>duan ini akan membantu Anda deploy kedua komponen SIMDOC:
- 🗄️ **Database Seeder** (simdoc-db-seeder)
- ⚡ **Firebase Functions** (simdoc-bapeltan)

## ⚡ DEPLOYMENT OTOMATIS (Recommended)

### Windows
```bash
# Jalankan script deployment otomatis
deploy-all.bat
```

### Linux/macOS
```bash
# Jalankan script deployment otomatis
./deploy-all.sh
```

## 📋 PERSIAPAN SEBELUM DEPLOYMENT

### 1. Download Service Account Key
1. Buka [Firebase Console](https://console.firebase.google.com/)
2. Pilih project: `simdoc-bapeltan`
3. Klik ⚙️ **Project Settings** → **Service Accounts**
4. Klik **Generate new private key**
5. Download file JSON
6. **Rename** menjadi `service-account-key.json`
7. **Letakkan** di folder `simdoc-db-seeder/`

### 2. Setup Google Cloud Permissions
1. Buka [Google Cloud Console IAM](https://console.developers.google.com/iam-admin/iam/project?project=simdoc-bapeltan)
2. Cari service account: `<EMAIL>`
3. Klik ✏️ **Edit** → **ADD ANOTHER ROLE**
4. Tambahkan roles:
   - ✅ **Service Usage Consumer**
   - ✅ **Firebase Admin SDK Administrator Service Agent**
   - ✅ **Cloud Datastore User**
   - ✅ **Firebase Authentication Admin**
5. **Save** dan tunggu 2-3 menit

### 3. Pastikan Firebase Project Blaze Plan
- Firebase Functions memerlukan **Blaze Plan** (pay-as-you-go)
- Upgrade di [Firebase Console](https://console.firebase.google.com/project/simdoc-bapeltan/usage)

## 🔧 DEPLOYMENT MANUAL (Step-by-Step)

### Step 1: Database Seeder
```bash
# Masuk ke folder seeder
cd simdoc-db-seeder

# Install dependencies
npm install

# Test koneksi
npm test

# Jalankan seeder
npm run seed:all

# Verifikasi data
npm run verify
```

### Step 2: Firebase Functions
```bash
# Masuk ke folder functions
cd simdoc-bapeltan

# Install dependencies
npm install

# Build TypeScript
npm run build

# Deploy functions
npm run deploy
```

### Step 3: Deploy dari Root
```bash
# Kembali ke root project
cd ..

# Deploy semua functions
firebase deploy --only functions
```

## ✅ VERIFIKASI DEPLOYMENT

### 1. Cek Database
- Buka [Firestore Console](https://console.firebase.google.com/project/simdoc-bapeltan/firestore)
- Pastikan collections ada:
  - ✅ `users` (5 users)
  - ✅ `categories` (10 categories)
  - ✅ `document-metadata` (sample docs)
  - ✅ `activities` (activity logs)

### 2. Cek Functions
```bash
# List deployed functions
firebase functions:list

# Test health check
curl https://us-central1-simdoc-bapeltan.cloudfunctions.net/healthCheck
```

### 3. Test Login
```
Admin: <EMAIL> / password123
User1: <EMAIL> / password123
User2: <EMAIL> / password123
User3: <EMAIL> / password123
User4: <EMAIL> / password123 (inactive)
```

## 🔧 TROUBLESHOOTING

### ❌ Error: "service-account-key.json not found"
**Solusi**: Download dan letakkan file service account key di `simdoc-db-seeder/`

### ❌ Error: "Caller does not have required permission"
**Solusi**: Tambah role "Service Usage Consumer" di Google Cloud Console IAM

### ❌ Error: "Build failed"
**Solusi**: 
```bash
cd simdoc-bapeltan
npm run lint --fix
npm run build
```

### ❌ Error: "Deployment failed"
**Solusi**: 
1. Pastikan Firebase project memiliki Blaze plan
2. Login ulang: `firebase login`
3. Set project: `firebase use simdoc-bapeltan`

## 📊 HASIL DEPLOYMENT

Setelah deployment berhasil, Anda akan memiliki:

### 🗄️ Database Collections
- **users**: 5 user accounts (1 admin, 4 regular users)
- **categories**: 10 realistic categories
- **document-metadata**: Sample documents with metadata
- **activities**: Activity logging system

### ⚡ Firebase Functions
- **healthCheck**: Health monitoring endpoint
- **getSystemStats**: System statistics API
- **onDocumentUpload**: Auto-trigger saat upload dokumen
- **onDocumentUpdate**: Auto-trigger saat update dokumen
- **onDocumentDelete**: Auto-trigger saat hapus dokumen

### 🔐 Authentication
- Firebase Authentication dengan 5 user accounts
- Role-based permissions (admin/user)
- Ready untuk login di Flutter app

## 🌐 Console Links

- **Functions**: https://console.firebase.google.com/project/simdoc-bapeltan/functions
- **Firestore**: https://console.firebase.google.com/project/simdoc-bapeltan/firestore
- **Authentication**: https://console.firebase.google.com/project/simdoc-bapeltan/authentication
- **Storage**: https://console.firebase.google.com/project/simdoc-bapeltan/storage

## 📝 Next Steps

1. ✅ Test login dengan credentials yang disediakan
2. ✅ Verifikasi data di Firebase Console
3. ✅ Test Flutter app functionality
4. ✅ Deploy Firestore security rules
5. ✅ Setup monitoring dan alerts
6. ✅ Configure backup strategies
7. ✅ Setup staging environment

## 🆘 Support

Jika mengalami masalah:
1. Cek logs: `firebase functions:log`
2. Verifikasi permissions di Google Cloud Console
3. Test koneksi: `cd simdoc-db-seeder && node test-connection.js`
4. Baca error messages dengan teliti
5. Pastikan semua prerequisites terpenuhi

---

**🎉 Selamat! SIMDOC backend Anda siap digunakan!**
