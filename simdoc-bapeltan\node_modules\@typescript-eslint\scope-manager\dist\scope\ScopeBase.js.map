{"version": 3, "file": "ScopeBase.js", "sourceRoot": "", "sources": ["../../src/scope/ScopeBase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,oDAA0D;AAE1D,sCAAmC;AAEnC,8CAA+C;AAC/C,8BAA0C;AAE1C,uDAIiC;AAEjC,0CAAuC;AAKvC,2CAAwC;AAGxC;;GAEG;AACH,SAAS,aAAa,CACpB,KAAY,EACZ,KAAoB,EACpB,kBAA2B;IAE3B,IAAI,IAAmE,CAAC;IAExE,qEAAqE;IACrE,IAAI,KAAK,CAAC,KAAK,EAAE,QAAQ,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,kBAAkB,EAAE,CAAC;QACvB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IACE,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,KAAK;QAC9B,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,eAAe;QACxC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,YAAY;QACrC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,UAAU;QACnC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM;QAC/B,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM;QAC/B,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,QAAQ;QACjC,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,IAAI,EAC7B,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,KAAK,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM,EAAE,CAAC;QACtE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,YAAY,GAAG,KAA+B,CAAC;QACrD,QAAQ,YAAY,CAAC,IAAI,EAAE,CAAC;YAC1B,KAAK,sBAAc,CAAC,uBAAuB;gBACzC,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;oBAC7D,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;gBACzB,MAAM;YAER,KAAK,sBAAc,CAAC,OAAO;gBACzB,IAAI,GAAG,YAAY,CAAC;gBACpB,MAAM;YAER;gBACE,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;QAC7B,CAAC;QAED,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;SAAM,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM,EAAE,CAAC;QAC3C,IAAI,GAAG,KAA6B,CAAC;IACvC,CAAC;SAAM,CAAC;QACN,OAAO,KAAK,CAAC;IACf,CAAC;IAED,iCAAiC;IACjC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;QAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,EAAE,CAAC;YACrD,MAAM;QACR,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,KAAK,YAAY,EAAE,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;QAC7B,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM;QACR,CAAC;QACD,IAAI,IAAI,CAAC,GAAG,KAAK,cAAc,IAAI,IAAI,CAAC,GAAG,KAAK,cAAc,EAAE,CAAC;YAC/D,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,IAAI,CAAC,KAAK,KAAK,YAAY,EAAE,CAAC;YAChC,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,aAAa,CAAC,YAA0B,EAAE,KAAY;IAC7D,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAEhC,MAAM,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IAEzD,IAAI,MAAM,EAAE,CAAC;QACX,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IACrB,CAAC;SAAM,CAAC;QACN,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACrD,CAAC;AACH,CAAC;AAED,MAAM,SAAS,GAAG,IAAA,sBAAiB,GAAE,CAAC;AAGtC,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAAC;IACnC,qBAAS,CAAC,qBAAqB;IAC/B,qBAAS,CAAC,gBAAgB;IAC1B,qBAAS,CAAC,QAAQ;IAClB,qBAAS,CAAC,MAAM;IAChB,qBAAS,CAAC,MAAM;IAChB,qBAAS,CAAC,QAAQ;CACnB,CAAC,CAAC;AAGH,MAAe,SAAS;IA0FtB,YACE,YAA0B,EAC1B,IAAW,EACX,UAAkB,EAClB,KAAa,EACb,kBAA2B;QA1F7B;;WAEG;QACa,QAAG,GAAW,SAAS,EAAE,CAAC;QAO1C;;;WAGG;QACa,gBAAW,GAAY,EAAE,CAAC;QAC1C;;;WAGG;QACM,+CAAuD;QAChE;;;;;WAKG;QACH,qCAAkB;QAClB;;;WAGG;QACa,4BAAuB,GAAY,KAAK,CAAC;QAMzD;;;WAGG;QACO,kBAAa,GAAuB,EAAE,CAAC;QACjD;;;;;;WAMG;QACa,eAAU,GAAgB,EAAE,CAAC;QAC7C;;;WAGG;QACa,QAAG,GAAG,IAAI,GAAG,EAAoB,CAAC;QAClD;;;WAGG;QACa,YAAO,GAAgB,EAAE,CAAC;QAW1C;;;;;;WAMG;QACa,cAAS,GAAe,EAAE,CAAC;QAkF3C,oCAAkB,CAAC,GAAc,EAAQ,EAAE;YACzC,MAAM,OAAO,GAAG,GAAY,EAAE;gBAC5B,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;gBACjC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAEpC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC;oBAC3C,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,gEAAgE;gBAChE,MAAM,oBAAoB,GACxB,GAAG,CAAC,eAAe,IAAI,QAAQ,CAAC,cAAc,CAAC;gBACjD,MAAM,qBAAqB,GACzB,GAAG,CAAC,gBAAgB,IAAI,QAAQ,CAAC,eAAe,CAAC;gBACnD,IAAI,CAAC,oBAAoB,IAAI,CAAC,qBAAqB,EAAE,CAAC;oBACpD,OAAO,KAAK,CAAC;gBACf,CAAC;gBAED,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC9B,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;gBAExB,OAAO,IAAI,CAAC;YACd,CAAC,CAAC;YAEF,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;gBACf,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;YACjC,CAAC;QACH,CAAC,EAAC;QAEF,qCAAmB,CAAC,GAAc,EAAQ,EAAE;YAC1C,yCAAyC;YACzC,IAAI,OAAO,GAAG,IAAoB,CAAC;YAEnC,GAAG,CAAC;gBACF,OAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAC3B,OAAO,GAAG,OAAQ,CAAC,KAAK,CAAC;YAC3B,CAAC,QAAQ,OAAO,EAAE;QACpB,CAAC,EAAC;QAEF,oCAAkB,CAAC,GAAc,EAAE,YAA0B,EAAQ,EAAE;YACrE,8DAA8D;YAC9D,yCAAyC;YACzC,IAAI,IAAI,CAAC,8BAA8B,CAAC,GAAG,EAAE,YAAY,CAAC,EAAE,CAAC;gBAC3D,uBAAA,IAAI,iCAAgB,MAApB,IAAI,EAAiB,GAAG,CAAC,CAAC;YAC5B,CAAC;iBAAM,CAAC;gBACN,uBAAA,IAAI,kCAAiB,MAArB,IAAI,EAAkB,GAAG,CAAC,CAAC;YAC7B,CAAC;QACH,CAAC,EAAC;QAtHA,MAAM,qBAAqB,GAAG,UAAU,CAAC;QAEzC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,uBAAA,IAAI,sBACF,IAAI,CAAC,IAAI,KAAK,qBAAS,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,qBAAS,CAAC,IAAI,MAAA,CAAC;QACjE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,eAAe,EAAE;YACzC,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,qBAAsB,CAAC,aAAa,CAAC;QACzC,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC;QAExB;;;WAGG;QACH,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC,IAAa,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;QAExE,8CAA8C;QAC9C,qBAAqB,EAAE,WAAW,CAAC,IAAI,CAAC,IAAa,CAAC,CAAC;QAEvD,uBAAA,IAAI,gCAAsB,YAAY,CAAC,iBAAiB,MAAA,CAAC;QAEzD,aAAa,CAAC,YAAY,EAAE,IAAa,CAAC,CAAC;IAC7C,CAAC;IAEO,eAAe;QACrB,OAAO,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAEM,qBAAqB;QAC1B,OAAO,CAAC,uBAAA,IAAI,0BAAS,CAAC;IACxB,CAAC;IAEO,8BAA8B,CACpC,GAAc,EACd,YAA0B;QAE1B,+EAA+E;QAC/E,MAAM,IAAI,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC;QAEjC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,KAAK,CAAC;QACf,CAAC;QACD,+BAA+B;QAE/B,oFAAoF;QACpF,IAAI,YAAY,CAAC,QAAQ,EAAE,EAAE,CAAC;YAC5B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,mEAAmE;QACnE,WAAW;QACX,0EAA0E;QAC1E,sEAAsE;QACtE,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;QAC3B,OAAO,CACL,IAAI,CAAC,MAAM,GAAG,CAAC;YACf,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;gBACf,IAAI,GAAG,CAAC,IAAI,KAAK,2BAAc,CAAC,QAAQ,IAAI,GAAG,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;oBACtE,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC,CAAC,CACH,CAAC;IACJ,CAAC;IAuDM,KAAK,CAAC,YAA0B;QACrC,IAAI,QAA8D,CAAC;QAEnE,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;YACjC,QAAQ,GAAG,uBAAA,IAAI,iCAAgB,CAAC;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAClC,QAAQ,GAAG,uBAAA,IAAI,kCAAiB,CAAC;QACnC,CAAC;aAAM,CAAC;YACN,QAAQ,GAAG,uBAAA,IAAI,iCAAgB,CAAC;QAClC,CAAC;QAED,8CAA8C;QAC9C,IAAA,eAAM,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,QAAQ,CAAC,GAAG,EAAE,YAAY,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAE1B,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED;;;OAGG;IACO,iBAAiB,CAAC,IAAe,EAAE,SAAmB;QAC9D,OAAO,IAAI,CAAC;IACd,CAAC;IAES,oBAAoB,CAAC,GAAc;QAC1C,IAAI,CAAC,KAA8B,EAAE,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;QAC/D,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACzB,CAAC;IAEO,0BAA0B,CAChC,QAAkB,EAClB,IAAsC;QAEtC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,IAAI,SAAS,GAAG,uBAAA,IAAI,oCAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAElD,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;YACtB,SAAS,GAAG,EAAE,CAAC;YACf,uBAAA,IAAI,oCAAmB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAC/C,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAES,cAAc,CACtB,cAAiC,EACjC,GAA0B,EAC1B,SAAqB,EACrB,IAAgC,EAChC,GAAsB;QAEtB,MAAM,IAAI,GACR,OAAO,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC;QAC5E,IAAI,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,QAAQ;gBACN,OAAO,cAAc,KAAK,QAAQ;oBAChC,CAAC,CAAC,IAAI,mBAAQ,CAAC,IAAI,EAAE,IAAa,CAAC;oBACnC,CAAC,CAAC,cAAc,CAAC;YACrB,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACxB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,GAAG,EAAE,CAAC;YACR,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACxB,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YACpD,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QACxD,CAAC;QACD,IAAI,IAAI,EAAE,CAAC;YACT,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;IACH,CAAC;IAEM,gBAAgB,CAAC,IAAyB,EAAE,GAAe;QAChE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACtE,CAAC;IAEM,uBAAuB,CAC5B,IAA4B,EAC5B,GAAe;QAEf,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACvE,CAAC;IAEM,cAAc,CACnB,IAAkD,EAClD,SAAwB,yBAAa,CAAC,IAAI,EAC1C,SAAsC,EACtC,mBAAoD,EACpD,IAAI,GAAG,KAAK;QAEZ,MAAM,GAAG,GAAG,IAAI,qBAAS,CACvB,IAAI,EACJ,IAAa,EACb,MAAM,EACN,SAAS,EACT,mBAAmB,EACnB,IAAI,EACJ,6BAAiB,CAAC,KAAK,CACxB,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAEM,aAAa,CAAC,IAAyB;QAC5C,MAAM,GAAG,GAAG,IAAI,qBAAS,CACvB,IAAI,EACJ,IAAa,EACb,yBAAa,CAAC,IAAI,EAClB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,6BAAiB,CAAC,IAAI,CACvB,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAEM,sBAAsB,CAAC,IAAyB;QACrD,MAAM,GAAG,GAAG,IAAI,qBAAS,CACvB,IAAI,EACJ,IAAa,EACb,yBAAa,CAAC,IAAI,EAClB,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,6BAAiB,CAAC,IAAI,GAAG,6BAAiB,CAAC,KAAK,CACjD,CAAC;QAEF,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC1B,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;CACF;AAEQ,8BAAS"}