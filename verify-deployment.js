#!/usr/bin/env node

/**
 * SIMDOC Deployment Verification Script
 * Verifies that all components are deployed and working correctly
 */

const https = require('https');
const { execSync } = require('child_process');

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

const log = {
  info: (msg) => console.log(`${colors.blue}ℹ️  ${msg}${colors.reset}`),
  success: (msg) => console.log(`${colors.green}✅ ${msg}${colors.reset}`),
  warning: (msg) => console.log(`${colors.yellow}⚠️  ${msg}${colors.reset}`),
  error: (msg) => console.log(`${colors.red}❌ ${msg}${colors.reset}`)
};

// Configuration
const config = {
  projectId: 'simdoc-bapeltan',
  region: 'us-central1',
  functions: [
    'healthCheck',
    'getSystemStats',
    'onDocumentUpload',
    'onDocumentUpdate',
    'onDocumentDelete'
  ],
  collections: [
    'users',
    'categories',
    'document-metadata',
    'activities'
  ]
};

/**
 * Make HTTP request
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        try {
          resolve({
            statusCode: res.statusCode,
            data: JSON.parse(data)
          });
        } catch (e) {
          resolve({
            statusCode: res.statusCode,
            data: data
          });
        }
      });
    }).on('error', reject);
  });
}

/**
 * Execute shell command
 */
function execCommand(command) {
  try {
    const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
    return { success: true, output: output.trim() };
  } catch (error) {
    return { success: false, error: error.message };
  }
}

/**
 * Verify Firebase CLI and project
 */
async function verifyFirebaseSetup() {
  log.info('Verifying Firebase setup...');
  
  // Check Firebase CLI
  const cliCheck = execCommand('firebase --version');
  if (!cliCheck.success) {
    log.error('Firebase CLI not found');
    return false;
  }
  log.success(`Firebase CLI: ${cliCheck.output}`);
  
  // Check project
  const projectCheck = execCommand('firebase use');
  if (!projectCheck.success) {
    log.error('No Firebase project selected');
    return false;
  }
  
  if (!projectCheck.output.includes(config.projectId)) {
    log.error(`Wrong project selected. Expected: ${config.projectId}`);
    return false;
  }
  log.success(`Project: ${config.projectId}`);
  
  return true;
}

/**
 * Verify deployed functions
 */
async function verifyFunctions() {
  log.info('Verifying Firebase Functions...');
  
  // List functions
  const listCheck = execCommand('firebase functions:list');
  if (!listCheck.success) {
    log.error('Failed to list functions');
    return false;
  }
  
  const deployedFunctions = listCheck.output;
  let functionsOk = 0;
  
  for (const funcName of config.functions) {
    if (deployedFunctions.includes(funcName)) {
      log.success(`Function deployed: ${funcName}`);
      functionsOk++;
    } else {
      log.warning(`Function not found: ${funcName}`);
    }
  }
  
  // Test health check endpoint
  try {
    const healthUrl = `https://${config.region}-${config.projectId}.cloudfunctions.net/healthCheck`;
    log.info(`Testing health check: ${healthUrl}`);
    
    const response = await makeRequest(healthUrl);
    if (response.statusCode === 200 && response.data.status === 'healthy') {
      log.success('Health check endpoint working');
      functionsOk++;
    } else {
      log.warning(`Health check failed: ${response.statusCode}`);
    }
  } catch (error) {
    log.warning(`Health check error: ${error.message}`);
  }
  
  return functionsOk > 0;
}

/**
 * Verify database seeding
 */
async function verifyDatabase() {
  log.info('Verifying database seeding...');
  
  try {
    // Change to seeder directory and run verification
    process.chdir('./simdoc-db-seeder');
    
    const verifyCheck = execCommand('node verify-data.js');
    if (verifyCheck.success) {
      log.success('Database verification passed');
      return true;
    } else {
      log.warning('Database verification had issues');
      log.warning(verifyCheck.error);
      return false;
    }
  } catch (error) {
    log.error(`Database verification error: ${error.message}`);
    return false;
  } finally {
    // Change back to root directory
    process.chdir('..');
  }
}

/**
 * Verify authentication
 */
async function verifyAuthentication() {
  log.info('Verifying Firebase Authentication...');
  
  const authCheck = execCommand('firebase auth:export --format=json auth-users.json');
  if (authCheck.success) {
    log.success('Authentication export successful');
    
    // Clean up temp file
    execCommand('rm -f auth-users.json');
    return true;
  } else {
    log.warning('Authentication verification failed');
    return false;
  }
}

/**
 * Generate deployment report
 */
function generateReport(results) {
  console.log('\n' + '='.repeat(50));
  console.log(`${colors.blue}📊 DEPLOYMENT VERIFICATION REPORT${colors.reset}`);
  console.log('='.repeat(50));
  
  const sections = [
    { name: 'Firebase Setup', status: results.firebase },
    { name: 'Functions Deployment', status: results.functions },
    { name: 'Database Seeding', status: results.database },
    { name: 'Authentication', status: results.auth }
  ];
  
  sections.forEach(section => {
    const status = section.status ? 
      `${colors.green}✅ PASSED${colors.reset}` : 
      `${colors.red}❌ FAILED${colors.reset}`;
    console.log(`${section.name.padEnd(25)} ${status}`);
  });
  
  const overallStatus = Object.values(results).every(r => r);
  console.log('\n' + '-'.repeat(50));
  
  if (overallStatus) {
    console.log(`${colors.green}🎉 DEPLOYMENT VERIFICATION SUCCESSFUL!${colors.reset}`);
    console.log(`${colors.green}All components are working correctly.${colors.reset}`);
    
    console.log(`\n${colors.blue}📝 Next Steps:${colors.reset}`);
    console.log('1. Test Flutter app login functionality');
    console.log('2. Verify document upload/download features');
    console.log('3. Check category management');
    console.log('4. Monitor function logs for any issues');
    console.log('5. Setup monitoring and alerts');
    
    console.log(`\n${colors.blue}🌐 Console Links:${colors.reset}`);
    console.log(`Functions: https://console.firebase.google.com/project/${config.projectId}/functions`);
    console.log(`Firestore: https://console.firebase.google.com/project/${config.projectId}/firestore`);
    console.log(`Authentication: https://console.firebase.google.com/project/${config.projectId}/authentication`);
    
  } else {
    console.log(`${colors.red}💥 DEPLOYMENT VERIFICATION FAILED!${colors.reset}`);
    console.log(`${colors.yellow}Some components are not working correctly.${colors.reset}`);
    
    console.log(`\n${colors.yellow}🔧 Troubleshooting:${colors.reset}`);
    console.log('1. Check Firebase project permissions');
    console.log('2. Verify service account key configuration');
    console.log('3. Ensure Blaze plan is active');
    console.log('4. Review deployment logs for errors');
    console.log('5. Run cleanup-deployment.bat and try again');
  }
  
  console.log('\n' + '='.repeat(50));
}

/**
 * Main verification function
 */
async function main() {
  console.log(`${colors.blue}🔍 SIMDOC DEPLOYMENT VERIFICATION${colors.reset}`);
  console.log(`${colors.blue}Checking all components...${colors.reset}\n`);
  
  const results = {
    firebase: false,
    functions: false,
    database: false,
    auth: false
  };
  
  try {
    // Run all verifications
    results.firebase = await verifyFirebaseSetup();
    results.functions = await verifyFunctions();
    results.database = await verifyDatabase();
    results.auth = await verifyAuthentication();
    
  } catch (error) {
    log.error(`Verification error: ${error.message}`);
  }
  
  // Generate report
  generateReport(results);
  
  // Exit with appropriate code
  const success = Object.values(results).every(r => r);
  process.exit(success ? 0 : 1);
}

// Run verification
if (require.main === module) {
  main().catch(error => {
    log.error(`Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main, verifyFirebaseSetup, verifyFunctions, verifyDatabase, verifyAuthentication };
