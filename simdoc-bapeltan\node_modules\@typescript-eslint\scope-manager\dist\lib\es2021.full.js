"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2021_full = void 0;
const dom_1 = require("./dom");
const dom_iterable_1 = require("./dom.iterable");
const es2021_1 = require("./es2021");
const scripthost_1 = require("./scripthost");
const webworker_importscripts_1 = require("./webworker.importscripts");
exports.es2021_full = {
    ...es2021_1.es2021,
    ...dom_1.dom,
    ...webworker_importscripts_1.webworker_importscripts,
    ...scripthost_1.scripthost,
    ...dom_iterable_1.dom_iterable,
};
//# sourceMappingURL=es2021.full.js.map