/**
 * SIMDOC Firebase Cloud Functions
 * Backend services for SIMDOC document management system
 */

import {onRequest, onCall} from "firebase-functions/v2/https";
import {onDocumentCreated, onDocumentUpdated, onDocumentDeleted} from "firebase-functions/v2/firestore";
import * as logger from "firebase-functions/logger";
import * as admin from "firebase-admin";

// Initialize Firebase Admin SDK
admin.initializeApp();

// =============================================================================
// HTTP FUNCTIONS
// =============================================================================

/**
 * Health check endpoint
 */
export const healthCheck = onRequest((request, response) => {
  logger.info("Health check called", {structuredData: true});
  response.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "simdoc-bapeltan",
    version: "1.0.0"
  });
});

/**
 * Get system statistics
 */
export const getSystemStats = onCall(async (request) => {
  try {
    const db = admin.firestore();

    // Get collection counts
    const [usersSnapshot, categoriesSnapshot, documentsSnapshot] = await Promise.all([
      db.collection("users").get(),
      db.collection("categories").get(),
      db.collection("document-metadata").get()
    ]);

    const stats = {
      users: {
        total: usersSnapshot.size,
        active: usersSnapshot.docs.filter(doc => doc.data().status === "active").length
      },
      categories: {
        total: categoriesSnapshot.size,
        active: categoriesSnapshot.docs.filter(doc => doc.data().isActive === true).length
      },
      documents: {
        total: documentsSnapshot.size,
        recent: documentsSnapshot.docs.filter(doc => {
          const uploadedAt = doc.data().uploadedAt;
          if (!uploadedAt) return false;
          const daysDiff = (Date.now() - uploadedAt.toDate().getTime()) / (1000 * 60 * 60 * 24);
          return daysDiff <= 7;
        }).length
      },
      timestamp: new Date().toISOString()
    };

    logger.info("System stats requested", {stats});
    return {success: true, data: stats};
  } catch (error) {
    logger.error("Error getting system stats", error);
    throw new Error("Failed to get system statistics");
  }
});

// =============================================================================
// FIRESTORE TRIGGERS
// =============================================================================

/**
 * Trigger when a new document is uploaded
 */
export const onDocumentUpload = onDocumentCreated(
  "document-metadata/{documentId}",
  async (event) => {
    const document = event.data;
    if (!document) {
      logger.warn("No document data in upload trigger");
      return;
    }

    const documentId = event.params.documentId;
    const documentData = document.data();

    logger.info("New document uploaded", {
      documentId,
      fileName: documentData.fileName,
      uploadedBy: documentData.uploadedBy
    });

    try {
      // Update category document count
      if (documentData.categoryId) {
        const categoryRef = admin.firestore().collection("categories").doc(documentData.categoryId);
        await categoryRef.update({
          documentCount: admin.firestore.FieldValue.increment(1)
        });
        logger.info("Updated category document count", {categoryId: documentData.categoryId});
      }

      // Log activity
      await admin.firestore().collection("activities").add({
        type: "document_upload",
        userId: documentData.uploadedBy,
        documentId: documentId,
        fileName: documentData.fileName,
        categoryId: documentData.categoryId || null,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        metadata: {
          fileSize: documentData.fileSize,
          fileType: documentData.fileType
        }
      });

    } catch (error) {
      logger.error("Error in document upload trigger", error);
    }
  }
);

/**
 * Trigger when a document is updated
 */
export const onDocumentUpdate = onDocumentUpdated(
  "document-metadata/{documentId}",
  async (event) => {
    const beforeData = event.data?.before.data();
    const afterData = event.data?.after.data();

    if (!beforeData || !afterData) {
      logger.warn("Missing document data in update trigger");
      return;
    }

    const documentId = event.params.documentId;

    logger.info("Document updated", {
      documentId,
      fileName: afterData.fileName
    });

    try {
      // Check if category changed
      if (beforeData.categoryId !== afterData.categoryId) {
        const batch = admin.firestore().batch();

        // Decrease count from old category
        if (beforeData.categoryId) {
          const oldCategoryRef = admin.firestore().collection("categories").doc(beforeData.categoryId);
          batch.update(oldCategoryRef, {
            documentCount: admin.firestore.FieldValue.increment(-1)
          });
        }

        // Increase count for new category
        if (afterData.categoryId) {
          const newCategoryRef = admin.firestore().collection("categories").doc(afterData.categoryId);
          batch.update(newCategoryRef, {
            documentCount: admin.firestore.FieldValue.increment(1)
          });
        }

        await batch.commit();
        logger.info("Updated category counts after document move", {
          from: beforeData.categoryId,
          to: afterData.categoryId
        });
      }

      // Log activity
      await admin.firestore().collection("activities").add({
        type: "document_update",
        userId: afterData.uploadedBy,
        documentId: documentId,
        fileName: afterData.fileName,
        categoryId: afterData.categoryId || null,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        metadata: {
          changes: {
            categoryChanged: beforeData.categoryId !== afterData.categoryId,
            oldCategory: beforeData.categoryId,
            newCategory: afterData.categoryId
          }
        }
      });

    } catch (error) {
      logger.error("Error in document update trigger", error);
    }
  }
);

/**
 * Trigger when a document is deleted
 */
export const onDocumentDelete = onDocumentDeleted(
  "document-metadata/{documentId}",
  async (event) => {
    const documentData = event.data?.data();
    if (!documentData) {
      logger.warn("No document data in delete trigger");
      return;
    }

    const documentId = event.params.documentId;

    logger.info("Document deleted", {
      documentId,
      fileName: documentData.fileName
    });

    try {
      // Update category document count
      if (documentData.categoryId) {
        const categoryRef = admin.firestore().collection("categories").doc(documentData.categoryId);
        await categoryRef.update({
          documentCount: admin.firestore.FieldValue.increment(-1)
        });
        logger.info("Updated category document count after deletion", {categoryId: documentData.categoryId});
      }

      // Log activity
      await admin.firestore().collection("activities").add({
        type: "document_delete",
        userId: documentData.uploadedBy,
        documentId: documentId,
        fileName: documentData.fileName,
        categoryId: documentData.categoryId || null,
        timestamp: admin.firestore.FieldValue.serverTimestamp(),
        metadata: {
          fileSize: documentData.fileSize,
          fileType: documentData.fileType
        }
      });

    } catch (error) {
      logger.error("Error in document delete trigger", error);
    }
  }
);
