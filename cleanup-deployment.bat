@echo off
echo ========================================
echo    SIMDOC DEPLOYMENT CLEANUP SCRIPT
echo ========================================
echo.

set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %YELLOW%This script will help you clean up failed deployments%NC%
echo %YELLOW%and reset the environment for a fresh deployment.%NC%
echo.

echo %RED%WARNING: This will remove deployed functions and database data!%NC%
echo.
set /p confirm="Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo %YELLOW%Cleanup cancelled.%NC%
    pause
    exit /b 0
)

echo.
echo %BLUE%Starting cleanup process...%NC%
echo.

:: Check Firebase CLI
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: Firebase CLI is not installed%NC%
    pause
    exit /b 1
)

:: Set project
echo %YELLOW%Setting Firebase project...%NC%
firebase use simdoc-bapeltan
if %errorlevel% neq 0 (
    echo %RED%ERROR: Failed to set Firebase project%NC%
    pause
    exit /b 1
)

echo.
echo %YELLOW%[1/4] Cleaning up Firebase Functions...%NC%
echo.

:: List current functions
echo Listing current functions...
firebase functions:list

:: Delete functions (if they exist)
echo.
echo Deleting functions...
firebase functions:delete healthCheck --force 2>nul
firebase functions:delete getSystemStats --force 2>nul
firebase functions:delete onDocumentUpload --force 2>nul
firebase functions:delete onDocumentUpdate --force 2>nul
firebase functions:delete onDocumentDelete --force 2>nul

echo %GREEN%✅ Functions cleanup completed%NC%

echo.
echo %YELLOW%[2/4] Cleaning up Database (Optional)...%NC%
echo.

set /p cleandb="Do you want to clean up database collections? (y/N): "
if /i "%cleandb%"=="y" (
    echo Cleaning up database...
    cd simdoc-db-seeder
    
    if exist cleanup.js (
        node cleanup.js
        if %errorlevel% equ 0 (
            echo %GREEN%✅ Database cleanup completed%NC%
        ) else (
            echo %YELLOW%⚠️ Database cleanup had issues%NC%
        )
    ) else (
        echo %YELLOW%⚠️ cleanup.js not found, skipping database cleanup%NC%
    )
    
    cd ..
) else (
    echo %BLUE%Database cleanup skipped%NC%
)

echo.
echo %YELLOW%[3/4] Cleaning up Build Artifacts...%NC%
echo.

:: Clean simdoc-bapeltan build files
if exist "simdoc-bapeltan\lib" (
    echo Removing simdoc-bapeltan build files...
    rmdir /s /q "simdoc-bapeltan\lib"
    echo %GREEN%✅ simdoc-bapeltan build files removed%NC%
) else (
    echo %BLUE%No simdoc-bapeltan build files to clean%NC%
)

:: Clean node_modules if requested
set /p cleannode="Do you want to clean node_modules? (y/N): "
if /i "%cleannode%"=="y" (
    echo Cleaning node_modules...
    
    if exist "simdoc-bapeltan\node_modules" (
        echo Removing simdoc-bapeltan node_modules...
        rmdir /s /q "simdoc-bapeltan\node_modules"
    )
    
    if exist "simdoc-db-seeder\node_modules" (
        echo Removing simdoc-db-seeder node_modules...
        rmdir /s /q "simdoc-db-seeder\node_modules"
    )
    
    echo %GREEN%✅ node_modules cleaned%NC%
) else (
    echo %BLUE%node_modules cleanup skipped%NC%
)

echo.
echo %YELLOW%[4/4] Resetting Firebase Configuration...%NC%
echo.

:: Reset Firebase cache
echo Clearing Firebase cache...
firebase logout 2>nul
firebase login --reauth 2>nul

echo %GREEN%✅ Firebase configuration reset%NC%

echo.
echo ========================================
echo %GREEN%   CLEANUP COMPLETED SUCCESSFULLY!%NC%
echo ========================================
echo.

echo %BLUE%📋 Cleanup Summary:%NC%
echo %GREEN%✅ Firebase Functions: Removed%NC%
if /i "%cleandb%"=="y" (
    echo %GREEN%✅ Database Collections: Cleaned%NC%
) else (
    echo %BLUE%ℹ️ Database Collections: Preserved%NC%
)
echo %GREEN%✅ Build Artifacts: Removed%NC%
if /i "%cleannode%"=="y" (
    echo %GREEN%✅ Node Modules: Cleaned%NC%
) else (
    echo %BLUE%ℹ️ Node Modules: Preserved%NC%
)

echo.
echo %BLUE%📝 Next Steps:%NC%
echo 1. Fix any issues that caused the deployment to fail
echo 2. Ensure service-account-key.json is in simdoc-db-seeder/
echo 3. Verify Google Cloud IAM permissions
echo 4. Run deploy-all.bat again for fresh deployment

echo.
echo %YELLOW%💡 Common Issues to Check:%NC%
echo - Service account key file location and permissions
echo - Firebase project Blaze plan activation
echo - Google Cloud IAM roles assignment
echo - Internet connectivity and firewall settings
echo - Node.js and npm versions compatibility

echo.
echo %GREEN%🔄 Ready for fresh deployment!%NC%
pause
