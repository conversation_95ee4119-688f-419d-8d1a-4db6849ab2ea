{"name": "simdoc-deployment", "version": "1.0.0", "description": "SIMDOC deployment and management scripts", "scripts": {"deploy": "node deploy-all.js", "deploy:windows": "deploy-all.bat", "deploy:unix": "./deploy-all.sh", "verify": "node verify-deployment.js", "cleanup": "cleanup-deployment.bat", "seed:db": "cd simdoc-db-seeder && npm run seed:all", "seed:verify": "cd simdoc-db-seeder && npm run verify", "functions:deploy": "cd simdoc-bapeltan && npm run deploy", "functions:build": "cd simdoc-bapeltan && npm run build", "functions:logs": "firebase functions:log", "functions:list": "firebase functions:list", "setup:seeder": "cd simdoc-db-seeder && npm install", "setup:functions": "cd simdoc-bapeltan && npm install", "setup:all": "npm run setup:seeder && npm run setup:functions", "test:connection": "cd simdoc-db-seeder && node test-connection.js", "test:health": "curl https://us-central1-simdoc-bapeltan.cloudfunctions.net/healthCheck", "firebase:login": "firebase login", "firebase:use": "firebase use simdoc-bapeltan", "firebase:init": "firebase init", "help": "echo 'Available commands:' && npm run | grep -E '^  [a-z]'"}, "keywords": ["simdoc", "firebase", "deployment", "flutter", "document-management"], "author": "SIMDOC Team", "license": "MIT", "devDependencies": {"firebase-tools": "^13.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/rahmathanifpurnama/simdoc-bapeltan-flutter"}}