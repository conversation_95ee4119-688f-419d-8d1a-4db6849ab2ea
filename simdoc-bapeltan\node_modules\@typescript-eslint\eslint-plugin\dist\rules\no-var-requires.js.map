{"version": 3, "file": "no-var-requires.js", "sourceRoot": "", "sources": ["../../src/rules/no-var-requires.ts"], "names": [], "mappings": ";;AACA,oDAAoE;AACpE,wEAAiE;AAEjE,kCAAqC;AASrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EAAE,2DAA2D;YACxE,WAAW,EAAE,aAAa;SAC3B;QACD,QAAQ,EAAE;YACR,SAAS,EAAE,iDAAiD;SAC7D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,KAAK,EAAE;wBACL,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;wBACzB,WAAW,EAAE,mDAAmD;qBACjE;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC;IAC/B,MAAM,CAAC,OAAO,EAAE,OAAO;QACrB,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CACxC,OAAO,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,CACpC,CAAC;QACF,SAAS,mBAAmB,CAAC,UAAkB;YAC7C,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAClE,CAAC;QACD,OAAO;YACL,uCAAuC,CACrC,IAA6B;gBAE7B,IACE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAClD,OAAO,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,QAAQ;oBAC3C,mBAAmB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAC5C,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,MAAM,MAAM,GACV,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;oBACjD,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM;oBACpB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;gBAElB,IACE;oBACE,sBAAc,CAAC,cAAc;oBAC7B,sBAAc,CAAC,gBAAgB;oBAC/B,sBAAc,CAAC,aAAa;oBAC5B,sBAAc,CAAC,cAAc;oBAC7B,sBAAc,CAAC,eAAe;oBAC9B,sBAAc,CAAC,kBAAkB;iBAClC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,EACvB,CAAC;oBACD,MAAM,QAAQ,GAAG,gBAAQ,CAAC,YAAY,CAAC,IAAA,uBAAQ,EAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC;oBAErE,IAAI,CAAC,QAAQ,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC;wBAClC,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,WAAW;yBACvB,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}