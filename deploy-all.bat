@echo off
echo ========================================
echo    SIMDOC COMPLETE DEPLOYMENT SCRIPT
echo ========================================
echo.

:: Set colors for better visibility
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%Starting complete SIMDOC deployment...%NC%
echo.

:: Check prerequisites
echo %YELLOW%[1/6] Checking prerequisites...%NC%
echo.

:: Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: Node.js is not installed or not in PATH%NC%
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)
echo %GREEN%✅ Node.js found:%NC%
node --version

:: Check npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: npm is not available%NC%
    pause
    exit /b 1
)
echo %GREEN%✅ npm found:%NC%
npm --version

:: Check Firebase CLI
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: Firebase CLI is not installed%NC%
    echo Installing Firebase CLI...
    npm install -g firebase-tools
    if %errorlevel% neq 0 (
        echo %RED%ERROR: Failed to install Firebase CLI%NC%
        pause
        exit /b 1
    )
)
echo %GREEN%✅ Firebase CLI found:%NC%
firebase --version

echo.
echo %YELLOW%[2/6] Setting up Firebase project...%NC%
echo.

:: Login to Firebase
echo Checking Firebase authentication...
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo %YELLOW%Please login to Firebase...%NC%
    firebase login
    if %errorlevel% neq 0 (
        echo %RED%ERROR: Firebase login failed%NC%
        pause
        exit /b 1
    )
)

:: Set project
echo Setting Firebase project...
firebase use simdoc-bapeltan
if %errorlevel% neq 0 (
    echo %RED%ERROR: Failed to set Firebase project%NC%
    echo Please ensure project 'simdoc-bapeltan' exists
    pause
    exit /b 1
)
echo %GREEN%✅ Firebase project set successfully%NC%

echo.
echo %YELLOW%[3/6] Deploying Database Seeder...%NC%
echo.

:: Check if service account key exists
if not exist "simdoc-db-seeder\service-account-key.json" (
    echo %RED%ERROR: service-account-key.json not found!%NC%
    echo.
    echo %YELLOW%Please follow these steps:%NC%
    echo 1. Open Firebase Console: https://console.firebase.google.com/
    echo 2. Select project: simdoc-bapeltan
    echo 3. Go to Project Settings ^> Service Accounts
    echo 4. Click "Generate new private key"
    echo 5. Download and rename to "service-account-key.json"
    echo 6. Place it in simdoc-db-seeder\ folder
    echo.
    pause
    exit /b 1
)

:: Install seeder dependencies
echo Installing seeder dependencies...
cd simdoc-db-seeder
npm install
if %errorlevel% neq 0 (
    echo %RED%ERROR: Failed to install seeder dependencies%NC%
    cd ..
    pause
    exit /b 1
)

:: Test connection
echo Testing Firebase connection...
node test-connection.js
if %errorlevel% neq 0 (
    echo %RED%ERROR: Firebase connection test failed%NC%
    echo Please check your service account key and permissions
    cd ..
    pause
    exit /b 1
)

:: Run seeder
echo Running database seeder...
node fix-all-issues.js
if %errorlevel% neq 0 (
    echo %YELLOW%WARNING: Seeder had issues, but continuing...%NC%
    echo You may need to run it manually later
)

cd ..
echo %GREEN%✅ Database seeder completed%NC%

echo.
echo %YELLOW%[4/6] Setting up Firebase Functions...%NC%
echo.

:: Setup simdoc-bapeltan functions
echo Setting up simdoc-bapeltan functions...
cd simdoc-bapeltan

:: Install dependencies
echo Installing function dependencies...
npm install
if %errorlevel% neq 0 (
    echo %RED%ERROR: Failed to install function dependencies%NC%
    cd ..
    pause
    exit /b 1
)

:: Build functions
echo Building TypeScript functions...
npm run build
if %errorlevel% neq 0 (
    echo %RED%ERROR: Failed to build functions%NC%
    cd ..
    pause
    exit /b 1
)

cd ..
echo %GREEN%✅ Functions setup completed%NC%

echo.
echo %YELLOW%[5/6] Deploying to Firebase...%NC%
echo.

:: Deploy all functions
echo Deploying Firebase Functions...
firebase deploy --only functions
if %errorlevel% neq 0 (
    echo %RED%ERROR: Functions deployment failed%NC%
    echo.
    echo %YELLOW%Common solutions:%NC%
    echo 1. Ensure Firebase project has Blaze plan
    echo 2. Check internet connection
    echo 3. Verify deployment permissions
    echo 4. Try: firebase login
    pause
    exit /b 1
)

echo %GREEN%✅ Functions deployed successfully%NC%

echo.
echo %YELLOW%[6/6] Verifying deployment...%NC%
echo.

:: Verify functions
echo Checking deployed functions...
firebase functions:list
if %errorlevel% neq 0 (
    echo %YELLOW%WARNING: Could not list functions%NC%
)

:: Verify database
echo Verifying database seeding...
cd simdoc-db-seeder
node verify-data.js
if %errorlevel% neq 0 (
    echo %YELLOW%WARNING: Database verification had issues%NC%
)
cd ..

echo.
echo ========================================
echo %GREEN%   DEPLOYMENT COMPLETED SUCCESSFULLY!%NC%
echo ========================================
echo.

echo %BLUE%📊 Deployment Summary:%NC%
echo %GREEN%✅ Database Seeder: Sample data created%NC%
echo %GREEN%✅ Firebase Functions: simdoc-bapeltan deployed%NC%
echo %GREEN%✅ Authentication: User accounts ready%NC%
echo %GREEN%✅ Firestore: Collections populated%NC%

echo.
echo %BLUE%🔐 Default Login Credentials:%NC%
echo Admin: <EMAIL> / password123
echo User1: <EMAIL> / password123
echo User2: <EMAIL> / password123
echo User3: <EMAIL> / password123
echo User4: <EMAIL> / password123 (inactive)

echo.
echo %BLUE%🌐 Firebase Console Links:%NC%
echo Functions: https://console.firebase.google.com/project/simdoc-bapeltan/functions
echo Firestore: https://console.firebase.google.com/project/simdoc-bapeltan/firestore
echo Authentication: https://console.firebase.google.com/project/simdoc-bapeltan/authentication

echo.
echo %BLUE%📝 Next Steps:%NC%
echo 1. Test login with provided credentials
echo 2. Verify data in Firebase Console
echo 3. Test Flutter app functionality
echo 4. Deploy updated Firestore security rules
echo 5. Configure monitoring and alerts

echo.
echo %GREEN%🎉 SIMDOC deployment is ready!%NC%
pause
