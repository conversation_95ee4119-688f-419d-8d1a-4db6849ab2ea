# 🚀 PANDUAN DEPLOYMENT SIMDOC LENGKAP

## 📋 Daftar Isi
1. [Persiapan <PERSON>wal](#persiapan-awal)
2. [Deploy Database Seeder](#deploy-database-seeder)
3. [Deploy Firebase Functions](#deploy-firebase-functions)
4. [Verifikasi Deployment](#verifikasi-deployment)
5. [Troubleshooting](#troubleshooting)

---

## 🎯 Persiapan Awal

### 1. Verifikasi P<PERSON>yarat
```bash
# Cek Node.js (minimal v18)
node --version

# Cek npm
npm --version

# Install Firebase CLI jika belum ada
npm install -g firebase-tools

# Verifikasi Firebase CLI
firebase --version
```

### 2. Setup Firebase Authentication
```bash
# Login ke Firebase
firebase login

# Verifikasi project yang aktif
firebase projects:list

# Set project aktif (jika belum)
firebase use simdoc-bapeltan
```

---

## 🗄️ BAGIAN 1: DEPLOY DATABASE SEEDER

### Step 1: Download Service Account Key

1. **Buka Firebase Console**
   - Kun<PERSON>gi: https://console.firebase.google.com/
   - Pilih project: `simdoc-bapeltan`

2. **Generate Service Account Key**
   - Klik ⚙️ **Project Settings**
   - Pilih tab **Service Accounts**
   - Klik **Generate new private key**
   - Download file JSON

3. **Setup File Konfigurasi**
   ```bash
   # Masuk ke folder seeder
   cd simdoc-db-seeder
   
   # Rename file yang didownload menjadi service-account-key.json
   # Letakkan di folder simdoc-db-seeder/
   ```

### Step 2: Install Dependencies & Setup
```bash
# Masuk ke folder seeder
cd simdoc-db-seeder

# Install dependencies
npm install

# Atau gunakan batch file (Windows)
install.bat
```

### Step 3: Setup Permissions (PENTING!)

1. **Buka Google Cloud Console IAM**
   - Kunjungi: https://console.developers.google.com/iam-admin/iam/project?project=simdoc-bapeltan

2. **Tambah Role ke Service Account**
   - Cari: `<EMAIL>`
   - Klik ✏️ **Edit**
   - Klik **ADD ANOTHER ROLE**
   - Tambahkan roles berikut:
     - ✅ **Service Usage Consumer**
     - ✅ **Firebase Admin SDK Administrator Service Agent**
     - ✅ **Cloud Datastore User**
     - ✅ **Firebase Authentication Admin**

3. **Tunggu 2-3 menit** untuk propagasi permissions

### Step 4: Test Koneksi
```bash
# Test koneksi Firebase
npm test
# atau
node test-connection.js

# Jika berhasil, lanjut ke seeding
```

### Step 5: Jalankan Database Seeding

#### Opsi A: Automatic Fix & Seed (Recommended)
```bash
# Jalankan fix otomatis dan seeding
node fix-all-issues.js
```

#### Opsi B: Manual Step-by-Step
```bash
# Seed semua data sekaligus
npm run seed:all
# atau
node seed-all.js

# Atau seed individual
npm run seed:users      # Users & Authentication
npm run seed:categories # Categories
npm run seed:documents  # Sample Documents
npm run seed:activities # Activity Logs
```

#### Opsi C: Windows Batch Files
```bash
# Setup authentication
setup-auth.bat

# Run seeder
run-seeder.bat
```

### Step 6: Verifikasi Data
```bash
# Verifikasi data yang telah di-seed
npm run verify
# atau
node verify-data.js
```

---

## ⚡ BAGIAN 2: DEPLOY FIREBASE FUNCTIONS

### Step 1: Setup simdoc-bapeltan Functions

```bash
# Masuk ke folder functions
cd simdoc-bapeltan

# Install dependencies
npm install
```

### Step 2: Develop Functions (Template Kosong)

Saat ini `simdoc-bapeltan/src/index.ts` masih template kosong. Mari kita buat contoh function:

```typescript
// simdoc-bapeltan/src/index.ts
import {onRequest} from "firebase-functions/v2/https";
import {onDocumentCreated} from "firebase-functions/v2/firestore";
import * as logger from "firebase-functions/logger";

// HTTP Function untuk health check
export const healthCheck = onRequest((request, response) => {
  logger.info("Health check called", {structuredData: true});
  response.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "simdoc-bapeltan"
  });
});

// Firestore Trigger untuk document metadata
export const onDocumentUpload = onDocumentCreated(
  "document-metadata/{documentId}",
  (event) => {
    const document = event.data;
    if (!document) {
      logger.warn("No document data");
      return;
    }
    
    logger.info("New document uploaded", {
      documentId: event.params.documentId,
      data: document.data()
    });
    
    // Add your business logic here
    // e.g., send notifications, update counters, etc.
  }
);
```

### Step 3: Build & Deploy Functions

#### Opsi A: Manual Commands
```bash
# Build TypeScript
npm run build

# Deploy functions
npm run deploy
# atau
firebase deploy --only functions:simdoc-bapeltan
```

#### Opsi B: Automated Scripts
```bash
# Deploy dengan script otomatis
npm run deploy

# Atau gunakan script yang ada di root project
firebase deploy --only functions
```

### Step 4: Deploy dari Root Project
```bash
# Kembali ke root project
cd ..

# Deploy semua functions (termasuk simdoc-bapeltan)
firebase deploy --only functions

# Atau deploy specific codebase
firebase deploy --only functions:simdoc-bapeltan
```

---

## ✅ BAGIAN 3: VERIFIKASI DEPLOYMENT

### 1. Verifikasi Database Seeder
```bash
# Cek di Firebase Console
# https://console.firebase.google.com/project/simdoc-bapeltan/firestore

# Verifikasi collections:
# ✅ users (5 users)
# ✅ categories (10 categories)  
# ✅ document-metadata (sample docs)
# ✅ activities (activity logs)
```

### 2. Verifikasi Firebase Functions
```bash
# Cek functions yang deployed
firebase functions:list

# Test health check function
curl https://us-central1-simdoc-bapeltan.cloudfunctions.net/healthCheck

# Cek logs
firebase functions:log
```

### 3. Test Login Credentials
```
Admin: <EMAIL> / password123
User1: <EMAIL> / password123
User2: <EMAIL> / password123
User3: <EMAIL> / password123
User4: <EMAIL> / password123 (inactive)
```

---

## 🔧 TROUBLESHOOTING

### Database Seeder Issues

#### Error: "service-account-key.json not found"
```bash
# Solusi:
1. Download service account key dari Firebase Console
2. Rename menjadi "service-account-key.json"
3. Letakkan di folder simdoc-db-seeder/
```

#### Error: "Caller does not have required permission"
```bash
# Solusi:
1. Buka Google Cloud Console IAM
2. Tambah role "Service Usage Consumer" ke service account
3. Tunggu 2-3 menit
4. Coba lagi
```

#### Error: "getaddrinfo ENOTFOUND metadata.google.internal"
```bash
# Solusi:
1. Pastikan service-account-key.json ada dan valid
2. Cek koneksi internet
3. Restart terminal/command prompt
```

### Firebase Functions Issues

#### Error: "Build failed"
```bash
# Solusi:
cd simdoc-bapeltan
npm run lint --fix
npm run build
```

#### Error: "Deployment failed"
```bash
# Solusi:
1. Pastikan Firebase project memiliki Blaze plan
2. Cek permissions: firebase login
3. Verifikasi project: firebase use
```

#### Error: "Node.js version mismatch"
```bash
# Solusi:
1. Update Node.js ke versi 18+
2. Update package.json engines jika perlu
```

---

## 📊 RINGKASAN DEPLOYMENT

### ✅ Yang Berhasil Di-deploy:
- 🗄️ **Database Seeder**: Sample data untuk development
- ⚡ **Firebase Functions**: Backend logic (simdoc-bapeltan)
- 🔐 **Authentication**: User accounts & permissions
- 📁 **Firestore Collections**: Structured data

### 🎯 Next Steps:
1. Develop lebih banyak Cloud Functions sesuai kebutuhan
2. Setup CI/CD pipeline untuk automated deployment
3. Configure monitoring & logging
4. Setup staging environment
5. Implement security rules testing

---

## 🎯 RINGKASAN FILE DEPLOYMENT YANG DIBUAT

### 📄 File Panduan
- `DEPLOYMENT_GUIDE.md` - Panduan lengkap deployment
- `DEPLOYMENT_README.md` - Quick start guide
- `IMPLEMENTATION_SUMMARY.md` - Summary implementasi

### 🚀 Script Deployment
- `deploy-all.bat` - Script deployment otomatis (Windows)
- `deploy-all.sh` - Script deployment otomatis (Linux/macOS)
- `package.json` - NPM scripts untuk deployment management

### 🔧 Script Utilitas
- `verify-deployment.js` - Verifikasi deployment berhasil
- `cleanup-deployment.bat` - Cleanup deployment gagal
- `simdoc-bapeltan/src/index.ts` - Firebase Functions lengkap

### 📋 Cara Penggunaan Cepat

#### Windows:
```bash
# Deploy semua komponen
deploy-all.bat

# Verifikasi deployment
node verify-deployment.js

# Cleanup jika gagal
cleanup-deployment.bat
```

#### Linux/macOS:
```bash
# Deploy semua komponen
./deploy-all.sh

# Verifikasi deployment
./verify-deployment.js

# Setup permissions
chmod +x *.sh
```

#### NPM Scripts:
```bash
# Setup dependencies
npm run setup:all

# Deploy (cross-platform)
npm run deploy:windows  # Windows
npm run deploy:unix     # Linux/macOS

# Verifikasi
npm run verify

# Test koneksi
npm run test:connection

# Lihat logs
npm run functions:logs
```

---

## 📞 Support

Jika mengalami masalah:
1. Cek logs: `firebase functions:log`
2. Verifikasi permissions di Google Cloud Console
3. Test koneksi: `node test-connection.js`
4. Jalankan verifikasi: `node verify-deployment.js`
5. Baca error messages dengan teliti
