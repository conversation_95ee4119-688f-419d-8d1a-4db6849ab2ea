{"version": 3, "file": "getParserServices.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/getParserServices.ts"], "names": [], "mappings": ";;;AAKA,+EAA4E;AAE5E,MAAM,sCAAsC,GAC1C,gLAAgL,CAAC;AAEnL,MAAM,4BAA4B,GAChC,mKAAmK,CAAC;AA+CtK,SAAS,iBAAiB,CACxB,OAA0D,EAC1D,+BAA+B,GAAG,KAAK;IAEvC,mFAAmF;IACnF,EAAE;IACF,mCAAmC;IACnC,yCAAyC;IACzC,yGAAyG;IACzG,sEAAsE;IACtE,0FAA0F;IAC1F,EAAE;IACF,qFAAqF;IACrF,wCAAwC;IACxC;IACE,mIAAmI;IACnI,OAAO,CAAC,cAAc,EAAE,qBAAqB,IAAI,IAAI;QACrD,gLAAgL;QAChL,OAAO,CAAC,cAAc,CAAC,qBAAqB,IAAI,IAAI,EACpD,CAAC;QACD,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAED,+EAA+E;IAC/E,uDAAuD;IACvD;IACE,mIAAmI;IACnI,OAAO,CAAC,cAAc,CAAC,OAAO,IAAI,IAAI;QACtC,CAAC,+BAA+B,EAChC,CAAC;QACD,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IACjC,CAAC;IAED,mIAAmI;IACnI,OAAO,OAAO,CAAC,cAAc,CAAC;AAChC,CAAC;AAcQ,8CAAiB;AAb1B,yDAAyD;AAEzD,SAAS,UAAU,CAAC,UAAkB;IACpC,MAAM,IAAI,KAAK,CACb,IAAA,yDAA2B,EAAC,UAAU,CAAC;QACrC,CAAC,CAAC,sCAAsC;QACxC,CAAC,CAAC;YACE,sCAAsC;YACtC,4BAA4B;SAC7B,CAAC,IAAI,CAAC,IAAI,CAAC,CACjB,CAAC;AACJ,CAAC"}