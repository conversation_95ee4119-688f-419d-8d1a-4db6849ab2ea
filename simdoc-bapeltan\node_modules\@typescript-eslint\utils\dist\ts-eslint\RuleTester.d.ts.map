{"version": 3, "file": "RuleTester.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/RuleTester.ts"], "names": [], "mappings": "AAEA,OAAO,KAAK,EAAE,cAAc,EAAE,eAAe,EAAE,MAAM,cAAc,CAAC;AACpE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAC9C,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,iBAAiB,CAAC;AACrD,OAAO,KAAK,EACV,2BAA2B,EAC3B,kBAAkB,EAClB,UAAU,EACV,2BAA2B,EAC5B,MAAM,QAAQ,CAAC;AAEhB,UAAU,aAAa,CAAC,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC;IAC1D;;;OAGG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC;IACtB;;OAEG;IACH,QAAQ,CAAC,GAAG,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAClD;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC;IAC3B;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IAClD;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACtC;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;IACjD;;OAEG;IACH,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,2BAA2B,CAAC,CAAC;IAC1D;;;OAGG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,UAAU,gBAAgB,CAAC,WAAW,SAAS,MAAM;IACnD;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC;IAChC;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;;OAGG;IACH,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;CAIzB;AAED,UAAU,eAAe,CACvB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC,CACpC,SAAQ,aAAa,CAAC,QAAQ,CAAC;IAC/B;;OAEG;IACH,QAAQ,CAAC,MAAM,EAAE,SAAS,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC;IACvD;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACjC;AAED,UAAU,aAAa,CAAC,WAAW,SAAS,MAAM;IAChD;;OAEG;IACH,QAAQ,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC;IACzB;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,2BAA2B,CAAC;IAC5C;;OAEG;IACH,QAAQ,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC;IAC5B;;OAEG;IACH,QAAQ,CAAC,OAAO,CAAC,EAAE,MAAM,CAAC;IAC1B;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,CAAC;IACvB;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC;IAChC;;OAEG;IACH,QAAQ,CAAC,WAAW,CAAC,EAAE,SAAS,gBAAgB,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI,CAAC;IACvE;;OAEG;IACH,QAAQ,CAAC,IAAI,CAAC,EAAE,cAAc,GAAG,eAAe,CAAC;CAIlD;AAED;;;GAGG;AACH,KAAK,+BAA+B,GAAG,CACrC,IAAI,EAAE,MAAM,EACZ,QAAQ,EAAE,MAAM,IAAI,KACjB,IAAI,CAAC;AAEV,UAAU,QAAQ,CAChB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC;IAGpC,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC;IAC9D,QAAQ,CAAC,OAAO,EAAE,SAAS,eAAe,CAAC,WAAW,EAAE,QAAQ,CAAC,EAAE,CAAC;CACrE;AACD,UAAU,gBAAiB,SAAQ,aAAa,CAAC,MAAM;IAErD,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC;IACxB,QAAQ,CAAC,aAAa,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;CAClD;AAED,OAAO,OAAO,cAAc;IAC1B;;;OAGG;gBACS,YAAY,CAAC,EAAE,gBAAgB;IAE3C;;;;;OAKG;IACH,GAAG,CAAC,WAAW,SAAS,MAAM,EAAE,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC,EAClE,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,EACvC,KAAK,EAAE,QAAQ,CAAC,WAAW,EAAE,QAAQ,CAAC,GACrC,IAAI;IAEP;;;OAGG;IACH,MAAM,KAAK,QAAQ,IAAI,+BAA+B,CAAC;IACvD,MAAM,KAAK,QAAQ,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAExE;;;OAGG;IACH,MAAM,KAAK,EAAE,IAAI,+BAA+B,CAAC;IACjD,MAAM,KAAK,EAAE,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAElE;;;OAGG;IACH,MAAM,KAAK,MAAM,IAAI,+BAA+B,CAAC;IACrD,MAAM,KAAK,MAAM,CAAC,KAAK,EAAE,+BAA+B,GAAG,SAAS,EAAE;IAEtE;;OAEG;IACH,UAAU,CAAC,WAAW,SAAS,MAAM,EAAE,QAAQ,SAAS,QAAQ,CAAC,OAAO,EAAE,CAAC,EACzE,IAAI,EAAE,MAAM,EACZ,IAAI,EACA,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,GACzC,UAAU,CAAC,WAAW,EAAE,QAAQ,CAAC,GACpC,IAAI;CACR;;AAED,cAAM,UAAW,SAAQ,eAA2C;CAAG;AAEvE,OAAO,EACL,eAAe,EACf,gBAAgB,EAChB,UAAU,EACV,gBAAgB,EAChB,+BAA+B,EAC/B,QAAQ,EACR,aAAa,EACb,aAAa,GACd,CAAC"}